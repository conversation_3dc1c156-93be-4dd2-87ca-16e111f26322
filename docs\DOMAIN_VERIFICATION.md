# Domain Verification System

Hệ thống xác thực domain tự động cho ứng dụng Next.js, tích hợp với API backend và cache cookie.

## Tính năng

- ✅ Xác thực domain tự động khi truy cập lần đầu
- ✅ <PERSON>ache kết quả xác thực trong cookie (24h)
- ✅ Chỉ lưu WebsiteInfo và WebsiteAuth vào global state
- ✅ Tách biệt logic xác thực khỏi UI components
- ✅ Hỗ trợ fallback với fetch API
- ✅ Cập nhật metadata động (title, description, favicon)
- ✅ Test utilities cho development

## Cấu trúc

```
services/
├── verifyDomain.ts          # Service xác thực domain
├── domainCookieService.ts   # Quản lý cache cookie

stores/
├── websiteStore.ts          # Global state với Zustand

hooks/
├── useDomainVerification.ts # Hook chính cho domain verification

components/
├── auth/
│   └── DomainAuthProvider.tsx  # Provider xử lý xác thực
├── layout/
│   ├── AppContent.tsx          # Wrapper cho app content
│   └── DynamicMetadata.tsx     # Cập nhật metadata động
└── dev/
    └── DomainTestLoader.tsx    # Load test utilities

utils/
└── domainTestUtils.ts       # Utilities để test

helpers/
└── domainHelper.ts          # Helper functions cho domain
```

## API Endpoint

```
GET https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=agent.lienvu.com
```

### Response Format

```json
{
  "prompt": {
    "_id": "string",
    "website": {
      "meta_title": "string",
      "meta_description": "string",
      "favicon": "string",
      "logo": "string",
      "thumbnail": "string"
    },
    "shop_id": "string"
  },
  "website": {
    "website_id": "string",
    "google_login": 2,
    "client_id": "string",
    "client_secret": "string"
  }
}
```

### Data Extraction

Từ response trên, hệ thống sẽ extract:

- **WebsiteInfo**: `prompt.website.{meta_title, meta_description, favicon, logo, thumbnail}`
- **WebsiteAuth**: `website.{google_login, client_id, client_secret, website_id}`
- **API IDs**:
  - `prompt._id` → để gọi API khác
  - `website.website_id` → để gửi request Google Login
  - `prompt.shop_id` → nếu cần

## Cách sử dụng

### 1. Hiển thị thông tin website

```tsx
import { WebsiteHeader, WebsiteLogo, WebsiteTitle } from '@/components/layout/WebsiteHeader';

function MyComponent() {
  return (
    <div>
      {/* Header đầy đủ */}
      <WebsiteHeader />

      {/* Hoặc từng component riêng */}
      <div className="flex items-center gap-4">
        <WebsiteLogo size={48} />
        <WebsiteTitle as="h1" className="text-2xl font-bold" />
      </div>
    </div>
  );
}
```

### 2. Lấy IDs cho API calls

```tsx
import { useApiIds, useGoogleLoginConfig } from '@/hooks/useWebsiteData';

function ApiComponent() {
  const { promptId, websiteId, canCallApi } = useApiIds();
  const { isEnabled, clientId, canUseGoogleLogin } = useGoogleLoginConfig();

  const handleApiCall = async () => {
    if (!canCallApi) return;

    // Sử dụng promptId cho API calls khác
    const response = await fetch('/api/some-endpoint', {
      method: 'POST',
      body: JSON.stringify({ prompt_id: promptId })
    });
  };

  const handleGoogleLogin = async () => {
    if (!canUseGoogleLogin) return;

    // Sử dụng websiteId cho Google Login
    const loginData = {
      website_id: websiteId,
      google_token: 'token_from_google'
    };
  };
}
```

### 3. Kiểm tra trạng thái

```tsx
import { useDomainVerificationStatus } from '@/hooks/useDomainVerification';

function StatusComponent() {
  const { isVerified, isLoading, error } = useDomainVerificationStatus();

  return (
    <div>
      <p>Verified: {isVerified ? 'Yes' : 'No'}</p>
      <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
      {error && <p>Error: {error}</p>}
    </div>
  );
}
```

### 4. Lấy thông tin website chi tiết

```tsx
import { useVerifiedWebsiteInfo, useWebsiteSEO } from '@/hooks/useWebsiteData';

function WebsiteInfoComponent() {
  const { websiteInfo, websiteAuth } = useVerifiedWebsiteInfo();
  const { title, description, favicon } = useWebsiteSEO();

  return (
    <div>
      {websiteInfo && (
        <>
          <img src={websiteInfo.logo} alt="Logo" />
          <h1>{websiteInfo.meta_title}</h1>
          <p>{websiteInfo.meta_description}</p>
        </>
      )}
      {websiteAuth?.google_login === 2 && (
        <p>Google Login enabled</p>
      )}
    </div>
  );
}
```

## Testing

### Browser Console

Trong development mode, các test utilities sẽ được load tự động:

```javascript
// Test xác thực domain hiện tại
DomainTestUtils.testDomainVerification()

// Test với domain cụ thể
DomainTestUtils.testDomainVerification('agent.lienvu.com')

// Test cache
DomainTestUtils.testCache()

// Xóa cache
DomainTestUtils.clearCache()

// Test với domain mẫu
DomainTestUtils.testWithSampleDomain()

// Hiển thị debug info
DomainTestUtils.showDebugInfo()
```

### Manual Testing

1. Mở browser console
2. Chạy `DomainTestUtils.showDebugInfo()` để xem thông tin hiện tại
3. Chạy `DomainTestUtils.testDomainVerification()` để test xác thực
4. Kiểm tra console logs để xem kết quả

## Cache Management

### Cookie Structure

```json
{
  "domain": "agent.lienvu.com",
  "verified": true,
  "timestamp": 1640995200000,
  "websiteInfo": { ... },
  "websiteAuth": { ... },
  "promptId": "...",
  "websiteId": "...",
  "shopId": "..."
}
```

### Cache Duration

- **Thời gian**: 24 giờ
- **Tự động xóa**: Khi hết hạn hoặc domain thay đổi
- **Manual clear**: `DomainTestUtils.clearCache()`

## Environment Variables

```env
NEXT_PUBLIC_NODE_API_BACKEND_URL=https://fchatai-api.salekit.com:3034
```

## Error Handling

1. **Network Error**: Tự động retry với fetch API
2. **Invalid Response**: Hiển thị error message
3. **Cache Expired**: Tự động xác thực lại
4. **Domain Mismatch**: Xóa cache và xác thực lại

## Development Notes

- Trong localhost, sử dụng domain mẫu `agent.lienvu.com` để test
- Test utilities chỉ load trong development mode
- Console logs chi tiết để debug
- Tách biệt hoàn toàn logic và UI

## Production Deployment

1. Đảm bảo `NEXT_PUBLIC_NODE_API_BACKEND_URL` được set đúng
2. Domain thực sẽ được sử dụng thay vì domain mẫu
3. Test utilities sẽ không được load
4. Cache sẽ hoạt động với secure cookies
