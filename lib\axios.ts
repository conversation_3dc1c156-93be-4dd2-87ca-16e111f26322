import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import { getCookie } from "cookies-next/client";
const axiosClient = (baseURL: string | undefined, token?: string) => {
    const axiosInstance: AxiosInstance = axios.create({
        responseType: "json",
        baseURL: baseURL,
        timeout: 30000, // 30 seconds timeout
        headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
        },
        // Handle CORS and SSL issues
        withCredentials: false,
        validateStatus: function (status) {
            return status >= 200 && status < 300; // default
        }
    })

    axiosInstance.interceptors.request.use(
        async (config) => {
            if (token) {
                config.headers.token = token;
            } else {
                const token = getCookie('token') as string | undefined;
                if (token) {
                    config.headers.token = token;
                }
            }

            return config;
        },
        (error: AxiosError) => {
            return Promise.reject(error);
        }
    );

    axiosInstance.interceptors.response.use(
        (response: AxiosResponse) => {
            console.log('🌐 Axios Response:', {
                status: response.status,
                statusText: response.statusText,
                url: response.config.url,
                data: response.data
            });
            return response;
        },
        (error: AxiosError) => {
            console.error('🌐 Axios Response Error:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                url: error.config?.url,
                data: error.response?.data
            });

            // Xử lý lỗi ở đây, ví dụ như redirect đến trang login nếu token hết hạn
            if (error.response && error.response.status === 401) {

            }
            return Promise.reject(error);
        }
    );
    return axiosInstance;
}
export default axiosClient;