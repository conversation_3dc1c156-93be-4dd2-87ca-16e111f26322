'use client'

import React, { useState } from 'react'
import { useDomainVerificationStatus } from '@/hooks/useDomainVerification'
import { getDomainForApi } from '@/helpers/domainHelper'

export const ErrorDebugPanel = () => {
    const { error, isLoading } = useDomainVerificationStatus()
    const [isExpanded, setIsExpanded] = useState(false)

    if (!error || isLoading) {
        return null
    }

    const domain = getDomainForApi()
    const apiUrl = `${process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL}/api/v1/global/prompt/domain?domain=${domain}`

    const handleTestRawApi = () => {
        if (typeof window !== 'undefined' && (window as any).DomainTestUtils) {
            (window as any).DomainTestUtils.testRawApiCall()
        }
    }

    const handleClearCache = () => {
        if (typeof window !== 'undefined' && (window as any).DomainTestUtils) {
            (window as any).DomainTestUtils.clearCache()
            window.location.reload()
        }
    }

    return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                        <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div className="flex-1">
                        <h3 className="text-sm font-medium text-red-800">
                            Domain Verification Error
                        </h3>
                        <p className="text-sm text-red-700 mt-1">
                            {error}
                        </p>
                    </div>
                </div>
                <button
                    type="button"
                    onClick={() => setIsExpanded(!isExpanded)}
                    className="text-red-400 hover:text-red-600"
                >
                    <svg className={`w-5 h-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </button>
            </div>

            {isExpanded && (
                <div className="mt-4 space-y-4">
                    {/* Debug Info */}
                    <div className="bg-red-100 rounded p-3">
                        <h4 className="text-sm font-medium text-red-800 mb-2">Debug Information</h4>
                        <div className="text-xs text-red-700 space-y-1">
                            <p><strong>Domain:</strong> {domain}</p>
                            <p><strong>API URL:</strong> {apiUrl}</p>
                            <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
                        </div>
                    </div>

                    {/* Common Issues */}
                    <div className="bg-red-100 rounded p-3">
                        <h4 className="text-sm font-medium text-red-800 mb-2">Common Issues & Solutions</h4>
                        <div className="text-xs text-red-700 space-y-2">
                            {error.includes('Cannot read properties of undefined') && (
                                <div className="bg-white p-2 rounded border border-red-200">
                                    <p className="font-medium">Issue: Invalid response structure</p>
                                    <p>The API response doesn't match the expected format.</p>
                                    <p className="text-red-600 mt-1">
                                        → Click "Test Raw API" to see the actual response structure
                                    </p>
                                </div>
                            )}
                            
                            {error.includes('Network Error') && (
                                <div className="bg-white p-2 rounded border border-red-200">
                                    <p className="font-medium">Issue: Network connection problem</p>
                                    <p>Cannot connect to the API server.</p>
                                    <p className="text-red-600 mt-1">
                                        → Check your internet connection and API server status
                                    </p>
                                </div>
                            )}
                            
                            {error.includes('404') && (
                                <div className="bg-white p-2 rounded border border-red-200">
                                    <p className="font-medium">Issue: Domain not found</p>
                                    <p>The domain is not registered in the system.</p>
                                    <p className="text-red-600 mt-1">
                                        → Verify the domain is correctly configured in the backend
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Debug Actions */}
                    <div className="flex gap-2">
                        <button
                            type="button"
                            onClick={handleTestRawApi}
                            className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded transition-colors"
                        >
                            Test Raw API
                        </button>
                        <button
                            type="button"
                            onClick={handleClearCache}
                            className="bg-red-600 hover:bg-red-700 text-white text-xs px-3 py-1 rounded transition-colors"
                        >
                            Clear Cache & Reload
                        </button>
                        <button
                            type="button"
                            onClick={() => window.location.reload()}
                            className="bg-gray-600 hover:bg-gray-700 text-white text-xs px-3 py-1 rounded transition-colors"
                        >
                            Reload Page
                        </button>
                    </div>

                    {/* Console Instructions */}
                    <div className="bg-red-100 rounded p-3">
                        <h4 className="text-sm font-medium text-red-800 mb-2">Console Debug Commands</h4>
                        <div className="text-xs text-red-700 font-mono bg-white p-2 rounded border border-red-200">
                            <div>DomainTestUtils.testRawApiCall()</div>
                            <div>DomainTestUtils.showDebugInfo()</div>
                            <div>DomainTestUtils.clearCache()</div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    )
}
