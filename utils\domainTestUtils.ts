import { verifyDomain } from '@/services/verifyDomain';
import { DomainCookieService } from '@/services/domainCookieService';
import { getDomainForApi } from '@/helpers/domainHelper';

/**
 * Utility functions để test domain verification
 */
export class DomainTestUtils {
    /**
     * Test raw API call để debug response
     */
    static async testRawApiCall(domain?: string): Promise<void> {
        const testDomain = domain || getDomainForApi();
        const apiUrl = `${process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL}/api/v1/global/prompt/domain?domain=${testDomain}`;

        console.log('🧪 Testing raw API call for:', testDomain);
        console.log('🌐 API URL:', apiUrl);

        try {
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            });

            console.log('📊 Response status:', response.status);
            console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));

            if (!response.ok) {
                console.error('❌ HTTP Error:', response.status, response.statusText);
                return;
            }

            const rawData = await response.text();
            console.log('📥 Raw response (as text):', rawData);

            try {
                const jsonData = JSON.parse(rawData);
                console.log('📊 Parsed JSON:', jsonData);
                console.log('📊 JSON structure:');
                console.log('- Has prompt:', !!jsonData.prompt);
                console.log('- Has website:', !!jsonData.website);
                if (jsonData.prompt) {
                    console.log('- prompt._id:', jsonData.prompt._id);
                    console.log('- prompt.website:', !!jsonData.prompt.website);
                    console.log('- prompt.shop_id:', jsonData.prompt.shop_id);
                }
                if (jsonData.website) {
                    console.log('- website.website_id:', jsonData.website.website_id);
                    console.log('- website.google_login:', jsonData.website.google_login);
                }
            } catch (parseError) {
                console.error('❌ Failed to parse JSON:', parseError);
                console.log('🔍 Trying to decode as base64...');
                try {
                    const decoded = atob(rawData);
                    console.log('🔓 Base64 decoded:', decoded);
                    const decodedJson = JSON.parse(decoded);
                    console.log('📊 Decoded JSON:', decodedJson);
                } catch (decodeError) {
                    console.error('❌ Failed to decode base64:', decodeError);
                }
            }
        } catch (error) {
            console.error('❌ Raw API call error:', error);
        }
    }

    /**
     * Test domain verification với domain cụ thể
     */
    static async testDomainVerification(domain?: string): Promise<void> {
        const testDomain = domain || getDomainForApi();

        console.log('🧪 Testing domain verification for:', testDomain);
        console.log('🌐 API URL:', `${process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL}/api/v1/global/prompt/domain?domain=${testDomain}`);

        try {
            const result = await verifyDomain(testDomain);

            if (result.success && result.data) {
                console.log('✅ Domain verification test successful!');
                console.log('📊 Response data:', result.data);

                // Validate structure before saving
                if (result.data.prompt && result.data.prompt.website && result.data.website) {
                    // Test lưu vào cookie
                    DomainCookieService.saveDomainVerification({
                        domain: testDomain,
                        verified: true,
                        websiteInfo: result.data.prompt.website,
                        websiteAuth: {
                            google_login: result.data.website.google_login,
                            client_id: result.data.website.client_id,
                            client_secret: result.data.website.client_secret,
                            website_id: result.data.website.website_id
                        },
                        promptId: result.data.prompt._id,
                        websiteId: result.data.website.website_id,
                        shopId: result.data.prompt.shop_id
                    });

                    console.log('✅ Test data saved to cookie');

                    // Test đọc từ cookie
                    const cachedData = DomainCookieService.getDomainVerification(testDomain);
                    console.log('📖 Cached data:', cachedData);
                } else {
                    console.error('❌ Invalid data structure, cannot save to cookie');
                }

            } else {
                console.error('❌ Domain verification test failed:', result.message);
            }
        } catch (error) {
            console.error('❌ Domain verification test error:', error);
        }
    }

    /**
     * Test cache functionality
     */
    static testCache(domain?: string): void {
        const testDomain = domain || getDomainForApi();

        console.log('🧪 Testing cache for domain:', testDomain);

        // Kiểm tra cache hiện tại
        const cachedData = DomainCookieService.getDomainVerification(testDomain);
        console.log('📖 Current cache:', cachedData);

        // Kiểm tra trạng thái verified
        const isVerified = DomainCookieService.isDomainVerified(testDomain);
        console.log('✅ Is domain verified:', isVerified);
    }

    /**
     * Clear cache
     */
    static clearCache(): void {
        console.log('🧪 Clearing domain verification cache');
        DomainCookieService.clearDomainVerification();
        console.log('✅ Cache cleared');
    }

    /**
     * Test với domain mẫu
     */
    static async testWithSampleDomain(): Promise<void> {
        const sampleDomain = 'agent.lienvu.com';
        console.log('🧪 Testing with sample domain:', sampleDomain);
        await this.testDomainVerification(sampleDomain);
    }

    /**
     * Set debug domain (lưu vào localStorage)
     */
    static setDebugDomain(domain: string): void {
        localStorage.setItem('debug_domain', domain);
        console.log('🔧 Debug domain set to:', domain);
        console.log('🔄 Reload page to use new domain');
    }

    /**
     * Clear debug domain
     */
    static clearDebugDomain(): void {
        localStorage.removeItem('debug_domain');
        console.log('🔧 Debug domain cleared');
        console.log('🔄 Reload page to use default domain');
    }

    /**
     * Hiển thị thông tin debug
     */
    static showDebugInfo(): void {
        const currentDomain = getDomainForApi();
        const apiUrl = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL;
        const debugDomain = localStorage.getItem('debug_domain');

        console.log('🔍 Domain Verification Debug Info:');
        console.log('- Current domain:', currentDomain);
        console.log('- Debug domain override:', debugDomain || 'None');
        console.log('- Actual hostname:', window.location.hostname);
        console.log('- API base URL:', apiUrl);
        console.log('- Full API URL:', `${apiUrl}/api/v1/global/prompt/domain?domain=${currentDomain}`);
        console.log('- Environment:', process.env.NODE_ENV);
        console.log('- Is localhost:', window.location.hostname === 'localhost');

        // Kiểm tra cache
        this.testCache(currentDomain);
    }
}

// Expose to window for easy testing in browser console
if (typeof window !== 'undefined') {
    (window as any).DomainTestUtils = DomainTestUtils;
    console.log('🧪 DomainTestUtils available in window.DomainTestUtils');
    console.log('📝 Available methods:');
    console.log('- DomainTestUtils.testRawApiCall() - Test raw API response');
    console.log('- DomainTestUtils.testDomainVerification() - Test full verification');
    console.log('- DomainTestUtils.testCache() - Test cache functionality');
    console.log('- DomainTestUtils.clearCache() - Clear cache');
    console.log('- DomainTestUtils.testWithSampleDomain() - Test with sample domain');
    console.log('- DomainTestUtils.setDebugDomain("domain.com") - Override domain for testing');
    console.log('- DomainTestUtils.clearDebugDomain() - Clear domain override');
    console.log('- DomainTestUtils.showDebugInfo() - Show debug information');
}
