import { useEffect, useCallback } from 'react';
import { useWebsiteStore } from '@/stores/websiteStore';
import { verifyDomain } from '@/services/verifyDomain';
import { getDomainForApi } from '@/helpers/domainHelper';

export interface UseDomainVerificationReturn {
    isVerified: boolean;
    isLoading: boolean;
    error: string | null;
    currentDomain: string | null;
    websiteInfo: any;
    websiteAuth: any;
    verifyCurrentDomain: () => Promise<void>;
    reset: () => void;
}

export const useDomainVerification = (): UseDomainVerificationReturn => {
    const {
        isVerified,
        isLoading,
        error,
        currentDomain,
        websiteInfo,
        websiteAuth,
        setLoading,
        setError,
        setDomainData,
        loadFromCache,
        reset
    } = useWebsiteStore();

    /**
     * Xác thực domain hiện tại
     */
    const verifyCurrentDomain = useCallback(async () => {
        const domain = getDomainForApi();
        
        console.log('🔍 Starting domain verification for:', domain);
        
        // Kiểm tra cache trước
        if (loadFromCache(domain)) {
            console.log('✅ Domain verification loaded from cache');
            return;
        }

        setLoading(true);
        setError(null);

        try {
            const result = await verifyDomain(domain);
            
            if (result.success && result.data) {
                console.log('✅ Domain verification successful:', result.data);
                setDomainData(result.data, domain);
            } else {
                const errorMessage = result.message || 'Domain verification failed';
                console.error('❌ Domain verification failed:', errorMessage);
                setError(errorMessage);
                setLoading(false);
            }
        } catch (error: any) {
            const errorMessage = error.message || 'Network error during domain verification';
            console.error('❌ Domain verification error:', error);
            setError(errorMessage);
            setLoading(false);
        }
    }, [setLoading, setError, setDomainData, loadFromCache]);

    /**
     * Auto-verify domain khi component mount
     */
    useEffect(() => {
        // Chỉ verify nếu chưa được verify
        if (!isVerified && !isLoading) {
            console.log('🚀 Auto-verifying domain on mount');
            verifyCurrentDomain();
        }
    }, [isVerified, isLoading, verifyCurrentDomain]);

    return {
        isVerified,
        isLoading,
        error,
        currentDomain,
        websiteInfo,
        websiteAuth,
        verifyCurrentDomain,
        reset
    };
};

/**
 * Hook đơn giản chỉ để kiểm tra trạng thái verification
 */
export const useDomainVerificationStatus = () => {
    const { isVerified, isLoading, error } = useWebsiteStore();
    
    return {
        isVerified,
        isLoading,
        error
    };
};

/**
 * Hook để lấy thông tin website đã được verify
 */
export const useVerifiedWebsiteInfo = () => {
    const { isVerified, websiteInfo, websiteAuth } = useWebsiteStore();
    
    return {
        isVerified,
        websiteInfo: isVerified ? websiteInfo : null,
        websiteAuth: isVerified ? websiteAuth : null
    };
};
