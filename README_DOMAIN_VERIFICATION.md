# Domain Verification System - Quick Start

## 🎯 Tính năng chính

✅ **<PERSON><PERSON><PERSON> thực domain tự động** khi truy cập web lần đầu  
✅ **Cache 24h** - không cần xác thực lại  
✅ **Chỉ lưu dữ liệu cần thiết** - WebsiteInfo và WebsiteAuth  
✅ **Tách biệt logic và UI** - dễ tái sử dụng  
✅ **Console log chi tiết** khi lưu thành công  

## 🚀 Cách sử dụng

### 1. Hiển thị thông tin website

```tsx
import { WebsiteHeader, WebsiteLogo, WebsiteTitle } from '@/components/layout/WebsiteHeader';

function MyPage() {
  return (
    <div>
      {/* Header đầy đủ với logo, title, description */}
      <WebsiteHeader />
      
      {/* Hoặc từng component riêng */}
      <div className="flex items-center gap-4">
        <WebsiteLogo size={48} />
        <WebsiteTitle as="h1" className="text-2xl font-bold" />
      </div>
    </div>
  );
}
```

### 2. Lấy IDs cho API calls

```tsx
import { useApiIds, useGoogleLoginConfig } from '@/hooks/useWebsiteData';

function MyComponent() {
  const { promptId, websiteId, canCallApi } = useApiIds();
  const { isEnabled, clientId, canUseGoogleLogin } = useGoogleLoginConfig();
  
  const handleApiCall = async () => {
    if (!canCallApi) return;
    
    // Sử dụng promptId cho API calls khác
    const response = await fetch('/api/some-endpoint', {
      method: 'POST',
      body: JSON.stringify({ prompt_id: promptId })
    });
  };
  
  const handleGoogleLogin = async () => {
    if (!canUseGoogleLogin) return;
    
    // Sử dụng websiteId cho Google Login
    const loginData = {
      website_id: websiteId,
      google_token: 'token_from_google'
    };
  };
}
```

## 📊 Dữ liệu được lưu

### WebsiteInfo (từ `prompt.website`)
- `meta_title` - Tiêu đề website
- `meta_description` - Mô tả website  
- `favicon` - Icon website
- `logo` - Logo website
- `thumbnail` - Ảnh thumbnail

### WebsiteAuth (từ `website`)
- `google_login` - Trạng thái Google Login (2 = enabled)
- `client_id` - Google OAuth Client ID
- `client_secret` - Google OAuth Client Secret
- `website_id` - ID website để gửi request

### API IDs
- `promptId` (từ `prompt._id`) - Để gọi API khác
- `websiteId` (từ `website.website_id`) - Để gửi request Google Login
- `shopId` (từ `prompt.shop_id`) - Nếu cần

## 🧪 Testing

### Browser Console
```javascript
// Hiển thị debug info
DomainTestUtils.showDebugInfo()

// Test xác thực domain
DomainTestUtils.testDomainVerification()

// Xóa cache và test lại
DomainTestUtils.clearCache()
```

### Demo Page
- Truy cập http://localhost:3000
- Xem thông tin chi tiết và test các tính năng
- Kiểm tra console logs

## 🔧 Hooks có sẵn

```tsx
// Lấy IDs cho API
const { promptId, websiteId, canCallApi } = useApiIds();

// Lấy config Google Login  
const { isEnabled, clientId, canUseGoogleLogin } = useGoogleLoginConfig();

// Lấy thông tin website
const { websiteInfo, websiteAuth } = useVerifiedWebsiteInfo();

// Lấy thông tin SEO
const { title, description, favicon } = useWebsiteSEO();

// Kiểm tra trạng thái
const { isVerified, isLoading, error } = useDomainVerificationStatus();
```

## 📁 Files quan trọng

- `services/verifyDomain.ts` - Service xác thực domain
- `stores/websiteStore.ts` - Global state
- `hooks/useWebsiteData.ts` - Hooks để lấy dữ liệu
- `components/layout/WebsiteHeader.tsx` - UI components
- `components/auth/DomainAuthProvider.tsx` - Provider chính

## 🌐 API Endpoint

```
GET https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=agent.lienvu.com
```

## ⚙️ Environment

```env
NEXT_PUBLIC_NODE_API_BACKEND_URL=https://fchatai-api.salekit.com:3034
```

---

**Lưu ý**: Trong localhost, hệ thống sử dụng domain mẫu `agent.lienvu.com` để test. Trong production, domain thực sẽ được sử dụng.
