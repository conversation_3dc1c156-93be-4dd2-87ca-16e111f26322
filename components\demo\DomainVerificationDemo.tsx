'use client'

import React from 'react'
import { useDomainAuth } from '@/components/auth/DomainAuthProvider'
import { useDomainVerificationStatus, useVerifiedWebsiteInfo } from '@/hooks/useDomainVerification'
import { useWebsiteData, useApiIds, useGoogleLoginConfig } from '@/hooks/useWebsiteData'
import { getDomainForApi, getCurrentDomain, isLocalEnvironment } from '@/helpers/domainHelper'
import { WebsiteHeader, WebsiteLogo, WebsiteTitle } from '@/components/layout/WebsiteHeader'
import { GoogleLoginExample } from '@/components/examples/GoogleLoginExample'
import { ErrorDebugPanel } from '@/components/debug/ErrorDebugPanel'

export const DomainVerificationDemo = () => {
    const { isVerified, websiteInfo, websiteAuth, retry } = useDomainAuth()
    const { isLoading, error } = useDomainVerificationStatus()
    const { websiteInfo: verifiedInfo, websiteAuth: verifiedAuth } = useVerifiedWebsiteInfo()
    const { promptId, websiteId, shopId, canCallApi } = useApiIds()
    const { isEnabled: googleLoginEnabled, clientId, canUseGoogleLogin } = useGoogleLoginConfig()

    const currentDomain = getCurrentDomain()
    const apiDomain = getDomainForApi()
    const isLocal = isLocalEnvironment()

    return (
        <div className="max-w-4xl mx-auto p-6 space-y-6">
            {/* Error Debug Panel */}
            <ErrorDebugPanel />

            {/* Website Header Demo */}
            {isVerified && (
                <WebsiteHeader className="mb-6" />
            )}

            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold mb-4 text-gray-900">
                    Domain Verification Demo
                </h2>

                {/* Domain Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-700 mb-2">Domain Information</h3>
                        <div className="space-y-1 text-sm">
                            <p><span className="font-medium">Current Domain:</span> {currentDomain}</p>
                            <p><span className="font-medium">API Domain:</span> {apiDomain}</p>
                            <p><span className="font-medium">Is Local:</span> {isLocal ? 'Yes' : 'No'}</p>
                        </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-700 mb-2">Verification Status</h3>
                        <div className="space-y-1 text-sm">
                            <p>
                                <span className="font-medium">Status:</span>
                                <span className={`ml-2 px-2 py-1 rounded text-xs ${isVerified ? 'bg-green-100 text-green-800' :
                                    isLoading ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-red-100 text-red-800'
                                    }`}>
                                    {isVerified ? 'Verified' : isLoading ? 'Loading' : 'Not Verified'}
                                </span>
                            </p>
                            <p><span className="font-medium">Loading:</span> {isLoading ? 'Yes' : 'No'}</p>
                            {error && <p><span className="font-medium text-red-600">Error:</span> {error}</p>}
                        </div>
                    </div>
                </div>

                {/* Website Info */}
                {isVerified && websiteInfo && (
                    <div className="bg-blue-50 p-4 rounded-lg mb-6">
                        <h3 className="font-semibold text-blue-700 mb-3">Website Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <p><span className="font-medium">Title:</span> {websiteInfo.meta_title}</p>
                                <p><span className="font-medium">Description:</span> {websiteInfo.meta_description}</p>
                            </div>
                            <div className="space-y-2">
                                <p><span className="font-medium">Logo:</span>
                                    {websiteInfo.logo && (
                                        <img src={websiteInfo.logo} alt="Logo" className="inline-block ml-2 h-8 w-auto" />
                                    )}
                                </p>
                                <p><span className="font-medium">Favicon:</span>
                                    {websiteInfo.favicon && (
                                        <img src={websiteInfo.favicon} alt="Favicon" className="inline-block ml-2 h-4 w-4" />
                                    )}
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* API IDs */}
                {isVerified && (
                    <div className="bg-purple-50 p-4 rounded-lg mb-6">
                        <h3 className="font-semibold text-purple-700 mb-3">API Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <p><span className="font-medium">Prompt ID:</span>
                                    <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-sm">{promptId}</code>
                                </p>
                                <p><span className="font-medium">Website ID:</span>
                                    <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-sm">{websiteId}</code>
                                </p>
                                <p><span className="font-medium">Shop ID:</span>
                                    <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-sm">{shopId}</code>
                                </p>
                            </div>
                            <div className="space-y-2">
                                <p><span className="font-medium">Can Call API:</span>
                                    <span className={`ml-2 px-2 py-1 rounded text-xs ${canCallApi ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                        {canCallApi ? 'Yes' : 'No'}
                                    </span>
                                </p>
                                <p className="text-sm text-gray-600">
                                    Use <strong>promptId</strong> for other API calls<br />
                                    Use <strong>websiteId</strong> for Google Login requests
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Website Auth */}
                {isVerified && websiteAuth && (
                    <div className="bg-green-50 p-4 rounded-lg mb-6">
                        <h3 className="font-semibold text-green-700 mb-3">Google Login Configuration</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <p><span className="font-medium">Status:</span>
                                    <span className={`ml-2 px-2 py-1 rounded text-xs ${googleLoginEnabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                                        {googleLoginEnabled ? 'Enabled' : 'Disabled'}
                                    </span>
                                </p>
                                <p><span className="font-medium">Google Login Value:</span> {websiteAuth.google_login}</p>
                                <p><span className="font-medium">Can Use:</span>
                                    <span className={`ml-2 px-2 py-1 rounded text-xs ${canUseGoogleLogin ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                        {canUseGoogleLogin ? 'Yes' : 'No'}
                                    </span>
                                </p>
                            </div>
                            <div className="space-y-2">
                                <p><span className="font-medium">Client ID:</span>
                                    <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs break-all">{websiteAuth.client_id}</code>
                                </p>
                                <p><span className="font-medium">Client Secret:</span>
                                    <code className="ml-2 px-2 py-1 bg-gray-100 rounded text-xs">
                                        {websiteAuth.client_secret ? '***hidden***' : 'Not set'}
                                    </code>
                                </p>
                            </div>
                        </div>
                    </div>
                )}

                {/* Actions */}
                <div className="flex gap-4">
                    <button
                        type="button"
                        onClick={retry}
                        disabled={isLoading}
                        className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        {isLoading ? 'Verifying...' : 'Retry Verification'}
                    </button>

                    <button
                        type="button"
                        onClick={() => {
                            if (typeof window !== 'undefined' && (window as any).DomainTestUtils) {
                                (window as any).DomainTestUtils.testRawApiCall();
                            } else {
                                console.log('DomainTestUtils not available');
                            }
                        }}
                        className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        Test Raw API
                    </button>

                    <button
                        type="button"
                        onClick={() => {
                            if (typeof window !== 'undefined' && (window as any).DomainTestUtils) {
                                (window as any).DomainTestUtils.showDebugInfo();
                            } else {
                                console.log('DomainTestUtils not available');
                            }
                        }}
                        className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        Show Debug Info
                    </button>

                    <button
                        type="button"
                        onClick={() => {
                            if (typeof window !== 'undefined' && (window as any).DomainTestUtils) {
                                (window as any).DomainTestUtils.clearCache();
                                window.location.reload();
                            } else {
                                console.log('DomainTestUtils not available');
                            }
                        }}
                        className="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        Clear Cache & Reload
                    </button>
                </div>

                {/* Development Note */}
                {process.env.NODE_ENV === 'development' && (
                    <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h4 className="font-semibold text-yellow-800 mb-2">Development Mode - Debug Tools</h4>
                        <div className="text-sm text-yellow-700 space-y-2">
                            <p>Open browser console to access DomainTestUtils for advanced testing:</p>
                            <div className="bg-yellow-100 p-2 rounded font-mono text-xs">
                                <div>DomainTestUtils.testRawApiCall() - Debug raw API response</div>
                                <div>DomainTestUtils.testDomainVerification() - Test full verification</div>
                                <div>DomainTestUtils.clearCache() - Clear cache and reload</div>
                                <div>DomainTestUtils.showDebugInfo() - Show current state</div>
                            </div>
                            <p className="text-yellow-600">
                                <strong>Tip:</strong> If you see "Cannot read properties of undefined",
                                use testRawApiCall() to debug the API response structure.
                            </p>
                        </div>
                    </div>
                )}
            </div>

            {/* Usage Examples */}
            {isVerified && (
                <GoogleLoginExample />
            )}
        </div>
    )
}
