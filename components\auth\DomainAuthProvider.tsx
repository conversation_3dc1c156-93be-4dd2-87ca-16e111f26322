'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDomainVerification } from '@/hooks/useDomainVerification';

interface DomainAuthContextType {
    isVerified: boolean;
    isLoading: boolean;
    error: string | null;
    websiteInfo: any;
    websiteAuth: any;
    retry: () => void;
}

const DomainAuthContext = createContext<DomainAuthContextType | undefined>(undefined);

export const useDomainAuth = () => {
    const context = useContext(DomainAuthContext);
    if (context === undefined) {
        throw new Error('useDomainAuth must be used within a DomainAuthProvider');
    }
    return context;
};

interface DomainAuthProviderProps {
    children: React.ReactNode;
}

export const DomainAuthProvider: React.FC<DomainAuthProviderProps> = ({ children }) => {
    const {
        isVerified,
        isLoading,
        error,
        websiteInfo,
        websiteAuth,
        verifyCurrentDomain
    } = useDomainVerification();

    const [showError, setShowError] = useState(false);

    // Hiển thị error sau một khoảng thời gian để tránh flash
    useEffect(() => {
        if (error && !isLoading) {
            const timer = setTimeout(() => setShowError(true), 1000);
            return () => clearTimeout(timer);
        } else {
            setShowError(false);
        }
    }, [error, isLoading]);

    const retry = () => {
        setShowError(false);
        verifyCurrentDomain();
    };

    // Hiển thị loading state
    if (isLoading) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h2 className="text-lg font-semibold text-gray-900 mb-2">
                        Đang xác thực domain...
                    </h2>
                    <p className="text-gray-600">
                        Vui lòng đợi trong giây lát
                    </p>
                </div>
            </div>
        );
    }

    // Hiển thị error state
    if (showError && error) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center max-w-md mx-auto p-6">
                    <div className="text-red-500 mb-4">
                        <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                        Xác thực domain thất bại
                    </h2>
                    <p className="text-gray-600 mb-6">
                        {error}
                    </p>
                    <button
                        type="button"
                        onClick={retry}
                        className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                    >
                        Thử lại
                    </button>
                </div>
            </div>
        );
    }

    // Nếu đã verify thành công, render children với context
    if (isVerified) {
        const contextValue: DomainAuthContextType = {
            isVerified,
            isLoading,
            error,
            websiteInfo,
            websiteAuth,
            retry
        };

        return (
            <DomainAuthContext.Provider value={contextValue}>
                {children}
            </DomainAuthContext.Provider>
        );
    }

    // Fallback - không nên xảy ra
    return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
                <h2 className="text-lg font-semibold text-gray-900 mb-2">
                    Đang khởi tạo...
                </h2>
            </div>
        </div>
    );
};