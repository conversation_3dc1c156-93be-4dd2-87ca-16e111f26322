"use client"

import { ArrowLeft } from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import React, { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { useWebsiteInfo } from '@/hooks/useWebsiteInfo'
import { useGoogleLoginConfig } from '@/hooks/useGoogleLoginConfig'
import { getLoginConfig } from '@/lib/loginConfig'
import { handleNextAuthGoogleLogin } from '@/lib/googleAuth'
import { loginWithEmail, setAuthData } from '@/lib/auth'
import { WebsiteHeader } from '@/components/ui/WebsiteHeader'


export default function LoginPage() {
    const [showPassword, setShowPassword] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [showGoogleHelper, setShowGoogleHelper] = useState(false)
    const [formData, setFormData] = useState({
        email: '',
        password: ''
    })
    const router = useRouter()
    const { websiteInfo } = useWebsiteInfo()
    const googleLoginConfig = useGoogleLoginConfig()

    // Lấy cấu hình login từ website info
    const loginConfig = getLoginConfig(websiteInfo)

    // Kiểm tra nếu không được phép login
    useEffect(() => {
        if (!loginConfig.canLogin) {
            toast.error(loginConfig.message || 'Không thể truy cập trang đăng nhập')
            router.push('/welcome')
        }
    }, [loginConfig, router])

    // Xử lý Google login với NextAuth
    const handleGoogleLoginClick = async () => {
        if (!googleLoginConfig) {
            toast.error('Google login chưa được cấu hình')
            return
        }

        setIsLoading(true)
        try {
            console.log('🔐 Using NextAuth for Google login')

            await handleNextAuthGoogleLogin(googleLoginConfig)

            toast.success('Đăng nhập Google thành công!')
            router.push('/')
        } catch (error) {
            console.error('❌ Google login error:', error)
            const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra khi đăng nhập Google'
            toast.error(errorMessage)

            // Hiển thị helper nếu có lỗi
            setShowGoogleHelper(true)
        } finally {
            setIsLoading(false)
        }
    }

    // Xử lý email/password login
    const handleEmailLogin = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!formData.email || !formData.password) {
            toast.error('Vui lòng nhập đầy đủ email và mật khẩu')
            return
        }

        setIsLoading(true)
        try {
            const response = await loginWithEmail({
                email: formData.email,
                password: formData.password
            })

            if (response.success) {
                // Lưu token từ response
                const tokenToSave = response.token;

                console.log('💾 Saving email auth data:', {
                    token: tokenToSave,
                    tokenType: typeof tokenToSave
                });

                // Sử dụng authStore để lưu token string nguyên bản
                setAuthData({
                    token: tokenToSave,
                    user: response.user
                });

                toast.success('Đăng nhập thành công!')
                router.push('/')
            } else {
                toast.error('Email hoặc mật khẩu không đúng')
            }
        } catch (error) {
            console.error('❌ Email login error:', error)
            toast.error('Có lỗi xảy ra khi đăng nhập')
        } finally {
            setIsLoading(false)
        }
    }

    // Nếu không được phép login, không hiển thị gì
    if (!loginConfig.canLogin) {
        return null
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="w-full max-w-md">
                {/* Header */}
                <WebsiteHeader />

                {/* Login Form */}
                <div className="bg-white rounded-2xl shadow-xl p-8 space-y-6">
                    <div className="text-center">
                        <h1 className="text-2xl font-bold text-gray-900 mb-2">Đăng nhập</h1>
                        <p className="text-gray-600">Chào mừng bạn quay trở lại</p>
                    </div>

                    {/* Google Login Button */}
                    {loginConfig.showGoogleLogin && (
                        <div className="space-y-4">
                            <button
                                type="button"
                                onClick={handleGoogleLoginClick}
                                disabled={isLoading}
                                className="w-full flex items-center justify-center gap-3 bg-white border border-gray-300 rounded-xl px-4 py-3 text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                <Image
                                    src="https://developers.google.com/identity/images/g-logo.png"
                                    alt="Google"
                                    width={20}
                                    height={20}
                                />
                                {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập với Google'}
                            </button>

                            {/* Error message */}
                            {showGoogleHelper && (
                                <div className="text-center p-3 bg-red-50 border border-red-200 rounded-lg">
                                    <p className="text-sm text-red-600 mb-2">
                                        Đăng nhập Google thất bại. Vui lòng thử lại.
                                    </p>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowGoogleHelper(false);
                                            handleGoogleLoginClick();
                                        }}
                                        className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                                    >
                                        Thử lại
                                    </button>
                                </div>
                            )}

                            {loginConfig.showEmailLogin && (
                                <div className="relative">
                                    <div className="absolute inset-0 flex items-center">
                                        <div className="w-full border-t border-gray-300" />
                                    </div>
                                    <div className="relative flex justify-center text-sm">
                                        <span className="px-2 bg-white text-gray-500">Hoặc</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Email/Password Login */}
                    {loginConfig.showEmailLogin && (
                        <form className="space-y-4 w-full" onSubmit={handleEmailLogin}>
                            <div>
                                <label htmlFor="email" className="block text-sm font-medium mb-1">Email</label>
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    required
                                    value={formData.email}
                                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                                    className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm"
                                    placeholder="Nhập email"
                                    disabled={isLoading}
                                />
                            </div>
                            <div>
                                <label htmlFor="password" className="block text-sm font-medium mb-1">Mật khẩu</label>
                                <div className="relative">
                                    <input
                                        id="password"
                                        name="password"
                                        type={showPassword ? "text" : "password"}
                                        autoComplete="current-password"
                                        required
                                        value={formData.password}
                                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                                        className="flex h-10 w-full rounded-3xl border border-gray-300 bg-background px-3 py-2 text-base ring-offset-background placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2  focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm pr-10"
                                        placeholder="Nhập mật khẩu"
                                        disabled={isLoading}
                                    />
                                    <button
                                        type="button"
                                        className="absolute inset-y-0 right-0 flex items-center pr-3"
                                        onClick={() => setShowPassword(!showPassword)}
                                    >
                                        {showPassword ? (
                                            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        ) : (
                                            <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                                            </svg>
                                        )}
                                    </button>
                                </div>
                            </div>
                            <button
                                type="submit"
                                disabled={isLoading}
                                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                {isLoading ? 'Đang đăng nhập...' : 'Đăng nhập'}
                            </button>
                        </form>
                    )}

                    {/* Back to home */}
                    <div className="text-center">
                        <button
                            type="button"
                            onClick={() => router.push('/')}
                            className="inline-flex items-center gap-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Quay về trang chủ
                        </button>
                    </div>
                </div>
            </div>
        </div>
    )
}
