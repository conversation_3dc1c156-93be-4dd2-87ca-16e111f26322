# Debug Guide - Domain Verification Error

## 🚨 Error: "Cannot read properties of undefined (reading 'website')"

Lỗi này xảy ra khi response từ API không có cấu trúc như mong đợi.

## 🔍 Cách debug

### 1. Ki<PERSON>m tra raw API response

Mở browser console và chạy:

```javascript
DomainTestUtils.testRawApiCall()
```

Lệnh này sẽ:
- Gọi API trực tiếp
- Hiển thị raw response
- Phân tích cấu trúc JSON
- Thử decode base64 nếu cần

### 2. Ki<PERSON>m tra với domain khác

Nếu domain hiện tại không hoạt động, thử với domain khác:

```javascript
// Test với domain cụ thể
DomainTestUtils.testRawApiCall('agent.trinhxuanthuy.id.vn')

// Hoặc set domain override
DomainTestUtils.setDebugDomain('agent.trinhxuanthuy.id.vn')
// Sau đó reload page
location.reload()
```

### 3. Xem debug info

```javascript
DomainTestUtils.showDebugInfo()
```

### 4. Clear cache và thử lại

```javascript
DomainTestUtils.clearCache()
location.reload()
```

## 📊 Expected API Response Structure

API cần trả về cấu trúc như sau:

```json
{
  "prompt": {
    "_id": "string",
    "website": {
      "meta_title": "string",
      "meta_description": "string",
      "favicon": "string",
      "logo": "string",
      "thumbnail": "string"
    },
    "shop_id": "string"
  },
  "website": {
    "website_id": "string",
    "google_login": 2,
    "client_id": "string",
    "client_secret": "string"
  }
}
```

## 🔧 Common Issues

### 1. Response là string thay vì object
- API trả về base64 encoded string
- Hệ thống sẽ tự động decode

### 2. Missing fields
- `prompt` object không tồn tại
- `prompt.website` không tồn tại  
- `website` object không tồn tại

### 3. Network issues
- CORS errors
- API server down
- Wrong API URL

## 🛠️ Quick Fixes

### Test với domain khác:
```javascript
DomainTestUtils.setDebugDomain('agent.lienvu.com')
location.reload()
```

### Reset về domain mặc định:
```javascript
DomainTestUtils.clearDebugDomain()
location.reload()
```

### Test API trực tiếp:
```javascript
fetch('https://fchatai-api.salekit.com:3034/api/v1/global/prompt/domain?domain=agent.lienvu.com')
  .then(r => r.text())
  .then(console.log)
```

## 📝 Debug Commands Reference

```javascript
// Test raw API response
DomainTestUtils.testRawApiCall()

// Test with specific domain
DomainTestUtils.testRawApiCall('your-domain.com')

// Set domain override
DomainTestUtils.setDebugDomain('your-domain.com')

// Clear domain override
DomainTestUtils.clearDebugDomain()

// Show current debug info
DomainTestUtils.showDebugInfo()

// Clear cache
DomainTestUtils.clearCache()

// Test full verification
DomainTestUtils.testDomainVerification()
```

## 🎯 Next Steps

1. **Chạy `testRawApiCall()`** để xem response thực tế
2. **So sánh với expected structure** ở trên
3. **Nếu structure khác**, cập nhật code để match
4. **Nếu API không hoạt động**, kiểm tra server/network
5. **Thử với domain khác** để xác định vấn đề

## 📞 Support

Nếu vẫn gặp vấn đề, cung cấp:
- Output của `testRawApiCall()`
- Domain đang test
- Environment (dev/prod)
- Browser console errors
