interface GoogleLoginConfig {
  clientId: string
  clientSecret?: string
  enabled: boolean
  websiteId: string
}

export const handleNextAuthGoogleLogin = async (config: GoogleLoginConfig) => {
  if (!config.enabled) {
    throw new Error('Google login is not enabled')
  }

  if (!config.clientId) {
    throw new Error('Google Client ID is not configured')
  }

  // Tạo Google OAuth URL
  const googleAuthUrl = new URL('https://accounts.google.com/o/oauth2/v2/auth')
  googleAuthUrl.searchParams.set('client_id', config.clientId)
  googleAuthUrl.searchParams.set('redirect_uri', `${window.location.origin}/auth/callback`)
  googleAuthUrl.searchParams.set('response_type', 'code')
  googleAuthUrl.searchParams.set('scope', 'openid email profile')
  googleAuthUrl.searchParams.set('state', config.websiteId)

  // Redirect đến Google OAuth
  window.location.href = googleAuthUrl.toString()
}
