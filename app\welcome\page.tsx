'use client'

import { useWebsiteStore } from '@/stores/websiteStore'
import { useDomainAuthContext } from '@/components/auth/DomainAuthProvider'

const WelcomePage = () => {
    const { promptId, websiteId, shopId, websiteInfo, websiteAuth, isVerified, isLoading, error } = useWebsiteStore()
    const { isLoading: authLoading, error: authError } = useDomainAuthContext()

    if (isLoading || authLoading) {
        return (
            <div className="p-6 max-w-md mx-auto mt-10 bg-white rounded-lg shadow-lg text-center">
                <div className="animate-spin inline-block w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mr-2"></div>
                Đang xác thực domain...
            </div>
        )
    }

    if (error || authError) {
        return (
            <div className="p-6 max-w-md mx-auto mt-10 bg-white rounded-lg shadow-lg">
                <h1 className="text-2xl font-bold text-red-600 mb-4">❌ Lỗi xác thực</h1>
                <p className="text-red-500">{error || authError}</p>
                <div className="mt-6">
                    <button
                        type="button"
                        onClick={() => window.location.reload()}
                        className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
                    >
                        Thử lại
                    </button>
                </div>
            </div>
        )
    }

    if (!isVerified) {
        return (
            <div className="p-6 max-w-md mx-auto mt-10 bg-white rounded-lg shadow-lg">
                <h1 className="text-2xl font-bold text-yellow-600 mb-4">⚠️ Chưa xác thực</h1>
                <p>Domain chưa được xác thực.</p>
            </div>
        )
    }

    return (
        <div className="p-6 max-w-2xl mx-auto mt-10 bg-white rounded-lg shadow-lg">
            <h1 className="text-2xl font-bold text-green-600 mb-6">🎉 Xác thực thành công</h1>

            {/* Basic Info */}
            <div className="mb-6">
                <h2 className="text-lg font-semibold mb-3">Thông tin cơ bản</h2>
                <div className="space-y-2 text-sm bg-gray-50 p-4 rounded">
                    <p><strong>Prompt ID:</strong> {promptId || 'Chưa có'}</p>
                    <p><strong>Website ID:</strong> {websiteId || 'Chưa có'}</p>
                    <p><strong>Shop ID:</strong> {shopId || 'Chưa có'}</p>
                </div>
            </div>

            {/* Website Info */}
            {websiteInfo && (
                <div className="mb-6">
                    <h2 className="text-lg font-semibold mb-3">Thông tin website</h2>
                    <div className="space-y-2 text-sm bg-blue-50 p-4 rounded">
                        <p><strong>Title:</strong> {websiteInfo.meta_title}</p>
                        <p><strong>Description:</strong> {websiteInfo.meta_description}</p>
                        <p><strong>Favicon:</strong> {websiteInfo.favicon}</p>
                        <p><strong>Logo:</strong> {websiteInfo.logo}</p>
                        <p><strong>Thumbnail:</strong> {websiteInfo.thumbnail}</p>
                    </div>
                </div>
            )}

            {/* Website Auth */}
            {websiteAuth && (
                <div className="mb-6">
                    <h2 className="text-lg font-semibold mb-3">Cấu hình xác thực</h2>
                    <div className="space-y-2 text-sm bg-green-50 p-4 rounded">
                        <p><strong>Google Login:</strong> {websiteAuth.google_login ? '✅ Enabled' : '❌ Disabled'}</p>
                        <p><strong>Client ID:</strong> {websiteAuth.client_id || 'Chưa có'}</p>
                        <p><strong>Client Secret:</strong> {websiteAuth.client_secret ? '***' : 'Chưa có'}</p>
                    </div>
                </div>
            )}

            <div className="mt-6">
                <a
                    href="/"
                    className="inline-block bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors"
                >
                    Tiếp tục đến trang chủ
                </a>
            </div>
        </div>
    )
}

export default WelcomePage
