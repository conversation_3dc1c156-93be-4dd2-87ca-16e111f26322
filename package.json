{"name": "data.fchat.ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@hookform/resolvers": "^3.9.1", "@qdrant/js-client-rest": "^1.14.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@rehype-pretty/transformers": "latest", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.20", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cookies-next": "^6.0.0", "crypto": "^1.0.1", "crypto-js": "^4.2.0", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.17.0", "fs": "latest", "gray-matter": "latest", "input-otp": "1.4.1", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.454.0", "mammoth": "^1.9.1", "mongoose": "^8.16.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "latest", "node-forge": "^1.3.1", "openai": "^5.8.2", "path": "latest", "pdf-parse": "^1.1.1", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-icons": "^5.5.0", "react-resizable-panels": "^2.1.7", "react-toastify": "^11.0.5", "react-type-animation": "^3.2.0", "recharts": "2.15.0", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "^5.0.5"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22", "@types/node-forge": "^1.3.11", "@types/pdf-parse": "^1.1.5", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}