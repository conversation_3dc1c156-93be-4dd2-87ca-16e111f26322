'use client'

import { useEffect } from 'react'

export const DomainTestLoader = () => {
    useEffect(() => {
        // Chỉ load trong development
        if (process.env.NODE_ENV === 'development') {
            import('@/utils/domainTestUtils').then(() => {
                console.log('🧪 Domain test utilities loaded');
            }).catch(error => {
                console.error('❌ Failed to load domain test utilities:', error);
            });
        }
    }, []);

    return null; // Component này không render gì
}
