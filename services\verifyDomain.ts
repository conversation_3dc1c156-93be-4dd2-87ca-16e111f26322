import axios from '@/lib/axios'

export const verifyDomain = async (domain: string) => {
    const baseURL = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL!;
    const axiosInstance = axios(baseURL);

    try {
        console.log('🌐 Calling API:', `${baseURL}/api/v1/global/prompt/domain?domain=${domain}`);
        const response = await axiosInstance.get(`/api/v1/global/prompt/domain?domain=${domain}`)
        console.log('✅ API call successful');
        return response.data
    } catch (error: any) {
        console.error('❌ API call failed:', error);

        // Fallback với fetch API nếu axios thất bại
        if (error.message === 'Network Error') {
            console.log('🔄 Retrying with fetch API...');
            try {
                const fetchResponse = await fetch(`${baseURL}/api/v1/global/prompt/domain?domain=${domain}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                });

                if (!fetchResponse.ok) {
                    throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
                }

                const data = await fetchResponse.json();
                console.log('✅ Fetch API successful');
                return data;
            } catch (fetchError) {
                console.error('❌ Fetch API also failed:', fetchError);
                throw fetchError;
            }
        }

        throw error;
    }
}
