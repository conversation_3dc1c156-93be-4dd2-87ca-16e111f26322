import axios from '@/lib/axios'
import { decodePayload } from '@/helpers/encryptHelper'

export interface DomainVerificationResponse {
    success: boolean;
    data?: {
        prompt: {
            _id: string;
            website: {
                meta_title: string;
                meta_description: string;
                favicon: string;
                logo: string;
                thumbnail: string;
            };
            shop_id: string;
        };
        website: {
            website_id: string;
            google_login: number;
            client_id: string;
            client_secret: string;
        };
    };
    message?: string;
}

export const verifyDomain = async (domain: string): Promise<DomainVerificationResponse> => {
    const baseURL = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL!;
    const axiosInstance = axios(baseURL);

    try {
        console.log('🌐 Verifying domain:', domain);
        console.log('🌐 API URL:', `${baseURL}/api/v1/global/prompt/domain?domain=${domain}`);

        const response = await axiosInstance.get(`/api/v1/global/prompt/domain?domain=${domain}`)

        console.log('✅ Raw API response:', response.data);

        // <PERSON><PERSON><PERSON>i mã dữ liệu nếu cần
        let decodedData = response.data;
        if (typeof response.data === 'string') {
            decodedData = decodePayload(response.data);
            console.log('🔓 Decoded data:', decodedData);
        }

        return {
            success: true,
            data: decodedData
        };
    } catch (error: any) {
        console.error('❌ Domain verification failed:', error);

        // Fallback với fetch API nếu axios thất bại
        if (error.message === 'Network Error') {
            console.log('🔄 Retrying with fetch API...');
            try {
                const fetchResponse = await fetch(`${baseURL}/api/v1/global/prompt/domain?domain=${domain}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                });

                if (!fetchResponse.ok) {
                    throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
                }

                const data = await fetchResponse.json();
                console.log('✅ Fetch API successful:', data);

                // Giải mã dữ liệu nếu cần
                let decodedData = data;
                if (typeof data === 'string') {
                    decodedData = decodePayload(data);
                    console.log('🔓 Decoded data:', decodedData);
                }

                return {
                    success: true,
                    data: decodedData
                };
            } catch (fetchError) {
                console.error('❌ Fetch API also failed:', fetchError);
                return {
                    success: false,
                    message: `Domain verification failed: ${fetchError}`
                };
            }
        }

        return {
            success: false,
            message: `Domain verification failed: ${error.message || error}`
        };
    }
}
