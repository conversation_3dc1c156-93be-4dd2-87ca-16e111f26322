'use client'

import React, { useState } from 'react'
import { useGoogleLoginConfig, useApiIds } from '@/hooks/useWebsiteData'

/**
 * Example component showing how to use Google Login with domain verification data
 */
export const GoogleLoginExample = () => {
    const { isEnabled, clientId, websiteId, canUseGoogleLogin } = useGoogleLoginConfig()
    const { promptId, canCallApi } = useApiIds()
    const [isLoading, setIsLoading] = useState(false)

    const handleGoogleLogin = async () => {
        if (!canUseGoogleLogin) {
            alert('Google Login is not enabled or configured properly')
            return
        }

        setIsLoading(true)
        
        try {
            // Example: Initialize Google Login
            console.log('🔐 Initializing Google Login with:')
            console.log('- Client ID:', clientId)
            console.log('- Website ID:', websiteId)
            
            // Here you would typically:
            // 1. Load Google OAuth library
            // 2. Initialize with clientId
            // 3. Handle login flow
            // 4. Send result to backend with websiteId
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000))
            
            // Example POST request structure:
            const examplePayload = {
                website_id: websiteId,
                google_token: 'example_token_from_google',
                user_info: {
                    email: '<EMAIL>',
                    name: 'User Name'
                }
            }
            
            console.log('📤 Would send POST request with:', examplePayload)
            alert('Google Login simulation completed! Check console for details.')
            
        } catch (error) {
            console.error('❌ Google Login error:', error)
            alert('Google Login failed')
        } finally {
            setIsLoading(false)
        }
    }

    const handleApiCall = async () => {
        if (!canCallApi) {
            alert('Cannot call API - missing required IDs')
            return
        }

        setIsLoading(true)
        
        try {
            console.log('📡 Making API call with:')
            console.log('- Prompt ID:', promptId)
            console.log('- Website ID:', websiteId)
            
            // Example API call structure
            const exampleApiUrl = `${process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL}/api/v1/some-endpoint`
            const examplePayload = {
                prompt_id: promptId,
                website_id: websiteId,
                // other data...
            }
            
            console.log('📤 Would call:', exampleApiUrl)
            console.log('📤 With payload:', examplePayload)
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1500))
            
            alert('API call simulation completed! Check console for details.')
            
        } catch (error) {
            console.error('❌ API call error:', error)
            alert('API call failed')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold mb-4 text-gray-900">
                Usage Examples
            </h3>
            
            <div className="space-y-4">
                {/* Google Login Example */}
                <div className="border rounded-lg p-4">
                    <h4 className="font-semibold text-gray-700 mb-2">Google Login Integration</h4>
                    <p className="text-sm text-gray-600 mb-3">
                        Example of how to use Google Login configuration from domain verification
                    </p>
                    
                    <div className="flex items-center gap-4">
                        <button
                            type="button"
                            onClick={handleGoogleLogin}
                            disabled={!canUseGoogleLogin || isLoading}
                            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                canUseGoogleLogin && !isLoading
                                    ? 'bg-red-600 hover:bg-red-700 text-white'
                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            }`}
                        >
                            {isLoading ? 'Processing...' : 'Simulate Google Login'}
                        </button>
                        
                        <div className="text-sm">
                            <span className={`px-2 py-1 rounded text-xs ${
                                canUseGoogleLogin ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                                {canUseGoogleLogin ? 'Ready' : 'Not Available'}
                            </span>
                        </div>
                    </div>
                    
                    {!canUseGoogleLogin && (
                        <p className="text-xs text-red-600 mt-2">
                            Google Login is not enabled (google_login !== 2) or missing client_id
                        </p>
                    )}
                </div>

                {/* API Call Example */}
                <div className="border rounded-lg p-4">
                    <h4 className="font-semibold text-gray-700 mb-2">API Call with Prompt ID</h4>
                    <p className="text-sm text-gray-600 mb-3">
                        Example of how to use prompt._id for other API calls
                    </p>
                    
                    <div className="flex items-center gap-4">
                        <button
                            type="button"
                            onClick={handleApiCall}
                            disabled={!canCallApi || isLoading}
                            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                canCallApi && !isLoading
                                    ? 'bg-blue-600 hover:bg-blue-700 text-white'
                                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                            }`}
                        >
                            {isLoading ? 'Processing...' : 'Simulate API Call'}
                        </button>
                        
                        <div className="text-sm">
                            <span className={`px-2 py-1 rounded text-xs ${
                                canCallApi ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                                {canCallApi ? 'Ready' : 'Not Available'}
                            </span>
                        </div>
                    </div>
                    
                    {!canCallApi && (
                        <p className="text-xs text-red-600 mt-2">
                            Missing required IDs (prompt_id or website_id)
                        </p>
                    )}
                </div>

                {/* Code Examples */}
                <div className="border rounded-lg p-4 bg-gray-50">
                    <h4 className="font-semibold text-gray-700 mb-2">Code Examples</h4>
                    
                    <div className="space-y-3">
                        <div>
                            <p className="text-sm font-medium text-gray-600 mb-1">Get Google Login Config:</p>
                            <code className="block text-xs bg-white p-2 rounded border">
                                {`const { isEnabled, clientId, websiteId, canUseGoogleLogin } = useGoogleLoginConfig()`}
                            </code>
                        </div>
                        
                        <div>
                            <p className="text-sm font-medium text-gray-600 mb-1">Get API IDs:</p>
                            <code className="block text-xs bg-white p-2 rounded border">
                                {`const { promptId, websiteId, canCallApi } = useApiIds()`}
                            </code>
                        </div>
                        
                        <div>
                            <p className="text-sm font-medium text-gray-600 mb-1">Get Website Info:</p>
                            <code className="block text-xs bg-white p-2 rounded border">
                                {`const { websiteInfo } = useVerifiedWebsiteInfo()`}
                            </code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
