export const getDomainForApi = (): string => {
    if (typeof window === 'undefined') {
        // Server-side: sử dụng domain mặc định
        return 'agent.lienvu.com';
    }

    // Kiểm tra nếu có override domain trong localStorage (để debug)
    const overrideDomain = localStorage.getItem('debug_domain');
    if (overrideDomain) {
        console.log('🔧 Using override domain from localStorage:', overrideDomain);
        return overrideDomain;
    }

    const hostname = window.location.hostname;
    const isLocal = hostname === 'localhost' ||
        hostname.startsWith('127.0.0.1') ||
        hostname.startsWith('192.168.');

    // Trong môi trường local, sử dụng domain mẫu để test
    return isLocal ? 'agent.lienvu.com' : hostname;
};

/**
 * Lấy domain hiện tại thực tế (không thay thế)
 */
export const getCurrentDomain = (): string => {
    if (typeof window === 'undefined') {
        return 'localhost';
    }
    return window.location.hostname;
};

/**
 * Kiểm tra xem có phải môi trường local không
 */
export const isLocalEnvironment = (): boolean => {
    if (typeof window === 'undefined') {
        return false;
    }

    const hostname = window.location.hostname;
    return hostname === 'localhost' ||
        hostname.startsWith('127.0.0.1') ||
        hostname.startsWith('192.168.');
};
