import { useDomainStore } from '@/stores/domainStore'

export const useGoogleLoginConfig = () => {
  const { websiteAuth } = useDomainStore()
  
  if (!websiteAuth?.google_login || !websiteAuth.client_id) {
    return null
  }
  
  return {
    clientId: websiteAuth.client_id,
    clientSecret: websiteAuth.client_secret,
    enabled: websiteAuth.google_login,
    websiteId: websiteAuth.website_id
  }
}
