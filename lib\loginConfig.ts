interface WebsiteInfo {
  meta_title?: string
  meta_description?: string
  favicon?: string
  logo?: string
  thumbnail?: string
}

interface LoginConfig {
  canLogin: boolean
  message?: string
  showGoogleLogin: boolean
  showEmailLogin: boolean
}

export const getLoginConfig = (websiteInfo: WebsiteInfo | null): LoginConfig => {
  // Nếu chưa có thông tin website, không cho phép login
  if (!websiteInfo) {
    return {
      canLogin: false,
      message: 'Đang tải thông tin website...',
      showGoogleLogin: false,
      showEmailLogin: false
    }
  }

  // Mặc định cho phép login
  return {
    canLogin: true,
    showGoogleLogin: true,
    showEmailLogin: true
  }
}
