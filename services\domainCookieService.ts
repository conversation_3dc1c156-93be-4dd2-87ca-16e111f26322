import { getCookie, set<PERSON><PERSON>ie, deleteCookie } from 'cookies-next';

export interface DomainVerificationCache {
    domain: string;
    verified: boolean;
    timestamp: number;
    websiteInfo?: {
        meta_title: string;
        meta_description: string;
        favicon: string;
        logo: string;
        thumbnail: string;
    };
    websiteAuth?: {
        google_login: number;
        client_id: string;
        client_secret: string;
        website_id: string;
    };
    promptId?: string;
    websiteId?: string;
    shopId?: string;
}

const COOKIE_NAME = 'domain_verification';
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

export class DomainCookieService {
    /**
     * Lưu thông tin xác thực domain vào cookie
     */
    static saveDomainVerification(data: Omit<DomainVerificationCache, 'timestamp'>): void {
        try {
            const cacheData: DomainVerificationCache = {
                ...data,
                timestamp: Date.now()
            };

            setCookie(COOKIE_NAME, JSON.stringify(cacheData), {
                maxAge: CACHE_DURATION / 1000, // maxAge in seconds
                httpOnly: false,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                path: '/'
            });

            console.log('✅ Domain verification saved to cookie:', data.domain);
        } catch (error) {
            console.error('❌ Failed to save domain verification to cookie:', error);
        }
    }

    /**
     * Lấy thông tin xác thực domain từ cookie
     */
    static getDomainVerification(domain: string): DomainVerificationCache | null {
        try {
            const cookieValue = getCookie(COOKIE_NAME);
            if (!cookieValue || typeof cookieValue !== 'string') {
                return null;
            }

            const cacheData: DomainVerificationCache = JSON.parse(cookieValue);

            // Kiểm tra domain có khớp không
            if (cacheData.domain !== domain) {
                console.log('🔄 Domain mismatch in cache, clearing...');
                this.clearDomainVerification();
                return null;
            }

            // Kiểm tra thời gian hết hạn
            const isExpired = Date.now() - cacheData.timestamp > CACHE_DURATION;
            if (isExpired) {
                console.log('⏰ Domain verification cache expired, clearing...');
                this.clearDomainVerification();
                return null;
            }

            console.log('✅ Valid domain verification found in cache:', domain);
            return cacheData;
        } catch (error) {
            console.error('❌ Failed to read domain verification from cookie:', error);
            this.clearDomainVerification();
            return null;
        }
    }

    /**
     * Kiểm tra xem domain đã được xác thực chưa
     */
    static isDomainVerified(domain: string): boolean {
        const cacheData = this.getDomainVerification(domain);
        return cacheData?.verified === true;
    }

    /**
     * Xóa thông tin xác thực domain khỏi cookie
     */
    static clearDomainVerification(): void {
        try {
            deleteCookie(COOKIE_NAME);
            console.log('🗑️ Domain verification cache cleared');
        } catch (error) {
            console.error('❌ Failed to clear domain verification cache:', error);
        }
    }

    /**
     * Cập nhật thời gian cache (refresh cache)
     */
    static refreshDomainVerification(domain: string): boolean {
        try {
            const cacheData = this.getDomainVerification(domain);
            if (!cacheData) {
                return false;
            }

            // Cập nhật timestamp
            cacheData.timestamp = Date.now();
            setCookie(COOKIE_NAME, JSON.stringify(cacheData), {
                maxAge: CACHE_DURATION / 1000,
                httpOnly: false,
                secure: process.env.NODE_ENV === 'production',
                sameSite: 'lax',
                path: '/'
            });

            console.log('🔄 Domain verification cache refreshed:', domain);
            return true;
        } catch (error) {
            console.error('❌ Failed to refresh domain verification cache:', error);
            return false;
        }
    }
}
