import { EP, QUERY } from "@/configs/constants/api";
import { getApiEndpoint } from "@/helpers/handleApiUrl";
import axiosClient from "@/lib/axios";
import { DataResponseType } from "@/types";

export const verifyDomain = async<T>(domain: string) => {
    const { data } = await axiosClient(process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL)
        .get<DataResponseType<T>>(getApiEndpoint([EP.API, EP.V1, EP.GLOBAL, EP.PROMPT, EP.DOMAIN],{
            [QUERY.DOMAIN]: domain,
        }));
    return data;
}
