import axios from '@/lib/axios'

export const verifyDomain = async (domain: string) => {
    const baseURL = process.env.NEXT_PUBLIC_NODE_API_BACKEND_URL!;

    try {
        // Method 1: Direct API call với axios
        const axiosInstance = axios(baseURL);
        const response = await axiosInstance.get(`/api/v1/global/prompt/domain?domain=${domain}`)
        return response.data
    } catch (error: any) {
        // Method 2: Fallback với fetch API nếu axios thất bại
        if (error.message === 'Network Error') {
            try {
                const fetchResponse = await fetch(`${baseURL}/api/v1/global/prompt/domain?domain=${domain}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                    },
                });

                if (!fetchResponse.ok) {
                    throw new Error(`HTTP ${fetchResponse.status}: ${fetchResponse.statusText}`);
                }

                const data = await fetchResponse.json();
                return data;
            } catch (fetchError) {
                // Method 3: Fallback to Next.js API route (server-side proxy)
                try {
                    const proxyResponse = await fetch(`/api/verify-domain?domain=${domain}`);

                    if (!proxyResponse.ok) {
                        throw new Error(`Proxy HTTP ${proxyResponse.status}: ${proxyResponse.statusText}`);
                    }

                    const proxyData = await proxyResponse.json();
                    return proxyData;
                } catch (proxyError) {
                    console.error('❌ All API methods failed:', proxyError);
                    throw proxyError;
                }
            }
        }

        throw error;
    }
}
