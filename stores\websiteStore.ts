import { create } from 'zustand';

interface WebsiteInfo {
    meta_title: string;
    meta_description: string;
    favicon: string;
    logo: string;
    thumbnail: string;
}

interface WebsiteAuth {
    google_login: boolean;
    client_id: string;
    client_secret: string;
    website_id: string;
}

interface DomainData {
    prompt: {
        _id: string;
        website: WebsiteInfo;
    };
    website: {
        website_id: string;
    };
    shop_id: string;
}

interface WebsiteStore {
    // Domain verification status
    isVerified: boolean;
    isLoading: boolean;
    error: string | null;

    // Basic domain data
    promptId: string | null;
    websiteId: string | null;
    shopId: string | null;

    // Detailed website info
    websiteInfo: WebsiteInfo | null;
    websiteAuth: WebsiteAuth | null;

    // Actions
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    setDomainData: (data: DomainData) => void;
    setWebsiteInfo: (info: WebsiteInfo) => void;
    setWebsiteAuth: (auth: WebsiteAuth) => void;
    reset: () => void;
}

export const useWebsiteStore = create<WebsiteStore>((set) => ({
    // Initial state
    isVerified: false,
    isLoading: false,
    error: null,
    promptId: null,
    websiteId: null,
    shopId: null,
    websiteInfo: null,
    websiteAuth: null,

    // Actions
    setLoading: (loading) => {
        console.log('🔄 Setting loading:', loading);
        set({ isLoading: loading });
    },

    setError: (error) => {
        console.log('❌ Setting error:', error);
        set({ error });
    },

    setDomainData: (data) => {
        console.log('🔄 Setting domain data:', data);
        set({
            promptId: data.prompt._id,
            websiteId: data.website.website_id,
            shopId: data.shop_id,
            websiteInfo: data.prompt.website,
            isVerified: true,
            isLoading: false,
            error: null
        });
        console.log('✅ Domain data saved to store');
    },

    setWebsiteInfo: (info) => {
        console.log('🔄 Setting website info:', info);
        set({ websiteInfo: info });
        console.log('✅ Website info saved to store');
    },

    setWebsiteAuth: (auth) => {
        console.log('🔄 Setting website auth:', auth);
        set({ websiteAuth: auth });
        console.log('✅ Website auth saved to store');
    },

    reset: () => {
        console.log('🔄 Resetting website store');
        set({
            isVerified: false,
            isLoading: false,
            error: null,
            promptId: null,
            websiteId: null,
            shopId: null,
            websiteInfo: null,
            websiteAuth: null
        });
    }
}));
